<template>
  <div class="debug-page">
    <div class="debug-container">
      <h1>用户状态调试页面</h1>
      
      <a-card title="当前用户状态" style="margin-bottom: 16px;">
        <p><strong>isLoggedIn:</strong> {{ userStore.isLoggedIn }}</p>
        <p><strong>token:</strong> {{ userStore.token }}</p>
        <p><strong>userId:</strong> {{ userStore.userId }}</p>
        <p><strong>userName:</strong> {{ userStore.userName }}</p>
        <p><strong>avatar:</strong> {{ userStore.avatar }}</p>
        <p><strong>role:</strong> {{ userStore.role }}</p>
        <p><strong>loading:</strong> {{ userStore.loading }}</p>
      </a-card>

      <a-card title="本地存储状态" style="margin-bottom: 16px;">
        <p><strong>localStorage.token:</strong> {{ localStorageData.token }}</p>
        <p><strong>localStorage.userId:</strong> {{ localStorageData.userId }}</p>
        <p><strong>localStorage.userName:</strong> {{ localStorageData.userName }}</p>
        <p><strong>localStorage.avatar:</strong> {{ localStorageData.avatar }}</p>
        <p><strong>localStorage.role:</strong> {{ localStorageData.role }}</p>
      </a-card>

      <a-card title="调试操作" style="margin-bottom: 16px;">
        <a-space>
          <a-button @click="refreshUserInfo" :loading="refreshing">
            刷新用户信息
          </a-button>
          <a-button @click="testLogin" :loading="testing">
            测试登录
          </a-button>
          <a-button @click="clearStorage" danger>
            清除本地存储
          </a-button>
        </a-space>
      </a-card>

      <a-card title="API测试结果">
        <pre>{{ debugInfo }}</pre>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '../stores/user'
import { getUserInfo } from '../api/user'
import { message } from 'ant-design-vue'

const userStore = useUserStore()
const refreshing = ref(false)
const testing = ref(false)
const debugInfo = ref('')

// 本地存储数据
const localStorageData = ref({
  token: '',
  userId: '',
  userName: '',
  avatar: '',
  role: ''
})

// 更新本地存储数据
const updateLocalStorageData = () => {
  localStorageData.value = {
    token: localStorage.getItem('token') || '',
    userId: localStorage.getItem('userId') || '',
    userName: localStorage.getItem('userName') || '',
    avatar: localStorage.getItem('avatar') || '',
    role: localStorage.getItem('role') || ''
  }
}

const refreshUserInfo = async () => {
  refreshing.value = true
  try {
    if (!userStore.userId) {
      debugInfo.value = '错误: 用户ID为空，无法获取用户信息'
      return
    }

    debugInfo.value = `开始获取用户信息，userId: ${userStore.userId}`

    const userInfo = await getUserInfo(userStore.userId)
    debugInfo.value += `\n\nAPI返回结果:\n${JSON.stringify(userInfo, null, 2)}`

    await userStore.getCurrentUserInfo()
    debugInfo.value += `\n\n用户状态已更新`

    // 更新本地存储显示
    updateLocalStorageData()

    message.success('用户信息刷新成功')
  } catch (error) {
    debugInfo.value += `\n\n错误: ${error.message}`
    message.error('刷新用户信息失败')
  } finally {
    refreshing.value = false
  }
}

const testLogin = async () => {
  testing.value = true
  try {
    debugInfo.value = '开始测试登录...'
    
    // 这里可以添加测试登录逻辑
    await userStore.login('test', 'test')
    
    debugInfo.value += '\n登录测试完成'
    message.success('登录测试完成')
  } catch (error) {
    debugInfo.value += `\n登录测试失败: ${error.message}`
    message.error('登录测试失败')
  } finally {
    testing.value = false
  }
}

const clearStorage = () => {
  localStorage.clear()
  debugInfo.value = '本地存储已清除'
  updateLocalStorageData()
  message.info('本地存储已清除')
  // 刷新页面
  setTimeout(() => {
    window.location.reload()
  }, 1000)
}

onMounted(() => {
  debugInfo.value = '调试页面已加载'
  updateLocalStorageData()

  // 添加实时监听用户状态变化
  setInterval(() => {
    updateLocalStorageData()
  }, 1000)
})
</script>

<style scoped>
.debug-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 24px;
}

.debug-container {
  max-width: 800px;
  margin: 0 auto;
}

pre {
  background-color: #f6f8fa;
  padding: 16px;
  border-radius: 6px;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
