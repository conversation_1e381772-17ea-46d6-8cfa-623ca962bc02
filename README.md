# 运动鞋商城前端项目

这是一个基于 Vue 3 + Vite 开发的运动鞋商城前端项目。

## 功能特点

- 商城首页
  - 分页滚动加载
  - 热门商品展示
  - 图片懒加载
  - 请求节流优化

- 搜索功能
  - 实时关键词联想
  - 搜索历史记录
  - 模糊匹配搜索

- 商品详情
  - 商品图库展示
  - 尺码选择
  - 库存状态显示
  - 加入购物车

- 购物车系统
  - 商品数量修改
  - 商品删除
  - 全选/反选
  - 实时价格计算

## 技术栈

- Vue 3
- Vite
- Vue Router
- Pinia
- Element Plus
- Axios

## 开发环境要求

- Node.js 16+
- npm 7+

## 安装和运行

1. 安装依赖：

```bash
npm install
```

2. 启动开发服务器：

```bash
npm run dev
```

3. 构建生产版本：

```bash
npm run build
```

## 项目结构

```
shoe-store/
├── src/
│   ├── assets/        # 静态资源
│   ├── components/    # 公共组件
│   ├── router/        # 路由配置
│   ├── stores/        # 状态管理
│   ├── views/         # 页面组件
│   ├── App.vue        # 根组件
│   └── main.js        # 入口文件
├── public/            # 公共资源
├── index.html         # HTML 模板
└── package.json       # 项目配置
```

## 注意事项

1. 本项目使用 Element Plus 作为 UI 组件库，确保已正确安装和配置。
2. 项目中的 API 调用需要根据实际后端接口进行修改。
3. 图片资源请替换为实际的商品图片。

## 待办事项

- [ ] 接入实际的后端 API
- [ ] 添加用户登录/注册功能
- [ ] 完善商品评价系统
- [ ] 优化移动端适配
- [ ] 添加更多支付方式
