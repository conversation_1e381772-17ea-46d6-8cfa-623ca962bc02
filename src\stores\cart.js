import { defineStore } from 'pinia'
import { addToCart as apiAddToCart, removeFromCart as apiRemoveFromCart, getCartList, updateCartItemQuantity, clearCart as apiClearCart } from '../api/cart'
import { useUserStore } from './user'

export const useCartStore = defineStore('cart', {
  state: () => ({
    items: [],
    loading: false,
    syncing: false // 同步状态
  }),

  getters: {
    totalPrice: (state) => {
      return state.items.reduce((total, item) => {
        return total + (item.price * item.quantity)
      }, 0)
    },
    totalQuantity: (state) => {
      return state.items.reduce((total, item) => total + item.quantity, 0)
    },
    totalItems: (state) => {
      return state.items.reduce((total, item) => total + item.quantity, 0)
    },
    // 获取购物车商品数量（用于显示徽章）
    cartItemCount: (state) => {
      return state.items.length
    }
  },

  actions: {
    // 从服务器加载购物车数据
    async loadCartFromServer() {
      const userStore = useUserStore()
      if (!userStore.isLoggedIn) {
        return
      }

      try {
        this.loading = true
        const cartData = await getCartList()
        this.items = cartData || []
      } catch (error) {
        console.error('加载购物车数据失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 添加商品到购物车
    async addToCart(product, quantity = 1, specModel = '') {
      const userStore = useUserStore()

      try {
        this.syncing = true

        if (userStore.isLoggedIn) {
          // 用户已登录，同步到服务器
          const cartItem = {
            userId: userStore.userId, // 保持字符串格式，避免精度丢失
            productId: product.id,
            specModel: specModel,
            quantity: quantity
          }

          await apiAddToCart(cartItem)
          // 重新加载购物车数据
          await this.loadCartFromServer()
        } else {
          // 用户未登录，只在本地存储
          const existingItem = this.items.find(item =>
            item.productId === product.id && item.specModel === specModel
          )

          if (existingItem) {
            if (existingItem.quantity + quantity <= 10) {
              existingItem.quantity += quantity
            }
          } else {
            this.items.push({
              id: Date.now(), // 临时ID
              productId: product.id,
              productName: product.productName,
              specModel: specModel,
              price: product.discountPrice,
              unit: product.unit,
              productImage: product.productImage,
              quantity: quantity
            })
          }
        }
      } catch (error) {
        console.error('添加到购物车失败:', error)
        throw error
      } finally {
        this.syncing = false
      }
    },

    // 从购物车移除商品
    async removeFromCart(cartItemId) {
      const userStore = useUserStore()

      try {
        this.syncing = true

        if (userStore.isLoggedIn) {
          // 用户已登录，从服务器删除
          await apiRemoveFromCart(cartItemId)
          // 重新加载购物车数据
          await this.loadCartFromServer()
        } else {
          // 用户未登录，从本地删除
          const index = this.items.findIndex(item => item.id === cartItemId)
          if (index > -1) {
            this.items.splice(index, 1)
          }
        }
      } catch (error) {
        console.error('从购物车移除商品失败:', error)
        throw error
      } finally {
        this.syncing = false
      }
    },

    // 更新商品数量
    async updateQuantity(cartItemId, quantity) {
      if (quantity <= 0 || quantity > 10) return

      const userStore = useUserStore()

      try {
        this.syncing = true

        if (userStore.isLoggedIn) {
          // 找到对应的购物车项
          const cartItem = this.items.find(item => item.id === cartItemId)
          if (cartItem) {
            const newCartItem = {
              userId: userStore.userId, // 保持字符串格式，避免精度丢失
              productId: cartItem.productId,
              specModel: cartItem.specModel,
              quantity: quantity
            }

            await updateCartItemQuantity(cartItemId, newCartItem)
            // 重新加载购物车数据
            await this.loadCartFromServer()
          }
        } else {
          // 用户未登录，更新本地数据
          const item = this.items.find(item => item.id === cartItemId)
          if (item) {
            item.quantity = quantity
          }
        }
      } catch (error) {
        console.error('更新商品数量失败:', error)
        throw error
      } finally {
        this.syncing = false
      }
    },

    // 清空购物车
    async clearCart() {
      const userStore = useUserStore()

      try {
        this.syncing = true

        if (userStore.isLoggedIn) {
          // 用户已登录，清空服务器购物车
          const cartItemIds = this.items.map(item => item.id)
          if (cartItemIds.length > 0) {
            await apiClearCart(cartItemIds)
          }
        }

        // 清空本地购物车
        this.items = []
      } catch (error) {
        console.error('清空购物车失败:', error)
        throw error
      } finally {
        this.syncing = false
      }
    },

    // 用户登录后同步本地购物车到服务器
    async syncLocalCartToServer() {
      const userStore = useUserStore()
      if (!userStore.isLoggedIn || this.items.length === 0) {
        return
      }

      try {
        this.syncing = true

        // 将本地购物车项逐个添加到服务器
        for (const item of this.items) {
          const cartItem = {
            userId: userStore.userId, // 保持字符串格式，避免精度丢失
            productId: item.productId,
            specModel: item.specModel || '',
            quantity: item.quantity
          }

          try {
            await apiAddToCart(cartItem)
          } catch (error) {
            console.error('同步购物车项失败:', error)
          }
        }

        // 重新从服务器加载购物车数据
        await this.loadCartFromServer()
      } catch (error) {
        console.error('同步本地购物车到服务器失败:', error)
        throw error
      } finally {
        this.syncing = false
      }
    }
  }
}) 