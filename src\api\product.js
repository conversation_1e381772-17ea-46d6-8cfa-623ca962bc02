import axios from 'axios'
import { processApiResponseIds, COMMON_ID_FIELDS } from '../utils/bigint'
import { STATUS_CODES, isAuthError } from '../utils/auth'

const BASE_URL = 'http://localhost:8000'

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  withCredentials: true // 支持跨域携带cookie
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 添加token认证信息
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    const { data } = response
    // 统一处理响应格式
    if (data.code === STATUS_CODES.SUCCESS) {
      // 处理响应数据中的大整数ID，避免精度丢失
      const processedData = processApiResponseIds(data, COMMON_ID_FIELDS)
      return processedData
    } else if (data.code === STATUS_CODES.NOT_LOGIN) {
      // 处理业务层面的未登录错误
      const authError = new Error(data.message || '未登录')
      authError.response = {
        status: STATUS_CODES.UNAUTHORIZED,
        data: { code: STATUS_CODES.NOT_LOGIN, message: data.message || '未登录' }
      }
      throw authError
    } else {
      throw new Error(data.message || '请求失败')
    }
  },
  error => {
    console.error('产品API请求错误:', error)

    // 检查是否为认证错误（HTTP 401 或业务状态码 1009）
    if (isAuthError(error)) {
      // 清除本地token（但不直接跳转，让调用方处理）
      localStorage.removeItem('token')
      localStorage.removeItem('userId')
      localStorage.removeItem('userName')
      localStorage.removeItem('avatar')
      localStorage.removeItem('role')

      // 注意：不在这里直接跳转，而是让调用方通过统一的认证错误处理函数处理
    }

    throw error
  }
)

// 获取商品列表
export const getProductList = async (page = 1, limit = 20, q = '') => {
  try {
    return await api.get('/product/list', {
      params: {
        page,
        limit,
        q
      }
    })
  } catch (error) {
    console.error('获取商品列表失败:', error)
    throw error
  }
}

// 获取商品详情
export const getProductDetail = async (id) => {
  try {
    return await api.get(`/product/detail/${id}`)
  } catch (error) {
    console.error('获取商品详情失败:', error)
    throw error
  }
}