import axios from 'axios'
import { processApiResponseIds, COMMON_ID_FIELDS } from '../utils/bigint'

const BASE_URL = 'http://localhost:8000'

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  withCredentials: true // 支持跨域携带cookie
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 添加token认证信息
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    const { data } = response
    // 统一处理响应格式
    if (data.code === 200) {
      // 处理响应数据中的大整数ID，避免精度丢失
      const processedData = processApiResponseIds(data, COMMON_ID_FIELDS)
      return processedData
    } else {
      throw new Error(data.message || '请求失败')
    }
  },
  error => {
    console.error('API请求错误:', error)

    // 处理401未授权错误
    if (error.response && error.response.status === 401) {
      // 清除本地token
      localStorage.removeItem('token')
      localStorage.removeItem('userId')
      localStorage.removeItem('userName')
      localStorage.removeItem('avatar')
      localStorage.removeItem('role')

      // 跳转到登录页
      if (typeof window !== 'undefined') {
        window.location.href = '/login'
      }
    }

    throw error
  }
)

// 获取商品列表
export const getProductList = async (page = 1, limit = 20, q = '') => {
  try {
    return await api.get('/product/list', {
      params: {
        page,
        limit,
        q
      }
    })
  } catch (error) {
    console.error('获取商品列表失败:', error)
    throw error
  }
}

// 获取商品详情
export const getProductDetail = async (id) => {
  try {
    return await api.get(`/product/detail/${id}`)
  } catch (error) {
    console.error('获取商品详情失败:', error)
    throw error
  }
}