<template>
  <div class="product-detail">
    <div class="detail-container">
      <div class="product-header">
        <h1 class="page-title">商品详情</h1>
        <div class="header-actions">
          <a-button @click="$router.back()" type="text">
            <template #icon>
              <arrow-left-outlined />
            </template>
            返回
          </a-button>
          <a-button @click="$router.push('/cart')" type="text">
            <template #icon>
              <shopping-cart-outlined />
            </template>
            <a-badge :count="cartStore.totalQuantity" :offset="[8, 0]">
              购物车
            </a-badge>
          </a-button>
        </div>
      </div>

      <!-- 加载状态 -->
      <a-spin :spinning="loading" tip="加载中..." size="large">
        <!-- 商品不存在 -->
        <a-empty v-if="!loading && !product" description="商品不存在">
          <template #extra>
            <a-button type="primary" @click="$router.push('/')">
              返回首页
            </a-button>
          </template>
        </a-empty>

        <!-- 商品详情内容 -->
        <div v-if="product" class="product-content">
          <!-- 商品图片轮播 -->
          <div class="product-gallery">
            <a-carousel autoplay>
              <div v-for="(image, index) in product.productImageList" :key="index">
                <div class="carousel-item">
                  <a-image
                    :src="image"
                    :preview="{
                      src: image,
                      visible: false,
                      onVisibleChange: (visible) => {
                        if (visible) {
                          previewImages = product.productImageList;
                          previewVisible = true;
                          previewCurrent = index;
                        }
                      }
                    }"
                    :fallback="'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN'"
                  />
                </div>
              </div>
            </a-carousel>

            <!-- 图片预览 -->
            <a-image-preview-group
              v-model:visible="previewVisible"
              :preview-group-props="{ current: previewCurrent }"
            >
              <a-image
                v-for="(image, index) in previewImages"
                :key="index"
                :src="image"
                style="display: none;"
              />
            </a-image-preview-group>
          </div>

          <!-- 商品信息 -->
          <div class="product-info">
            <h1 class="product-name">{{ product.productName }}</h1>

            <!-- 价格信息 -->
            <div class="price-section">
              <div class="current-price">{{ product.unit }}{{ product.discountPrice }}</div>
              <div v-if="product.originalPrice > product.discountPrice" class="original-price">
                {{ product.unit }}{{ product.originalPrice }}
              </div>
              <a-tag v-if="product.discount < 1" color="red">
                {{ Math.round((1 - product.discount) * 100) }}% OFF
              </a-tag>
            </div>

            <!-- 销售信息 -->
            <div class="sales-info">
              <a-statistic title="销量" :value="product.salesVolume" :value-style="{ fontSize: '16px' }" />
              <a-statistic title="库存" :value="product.stock" :value-style="{ fontSize: '16px' }" />
              <a-tag v-if="product.stock <= 10" color="orange">库存紧张</a-tag>
              <a-tag v-if="product.stock <= 0" color="red">缺货</a-tag>
            </div>

            <!-- 商品描述 -->
            <a-divider />
            <div class="product-description">
              <h3>商品描述</h3>
              <p>{{ product.description || '暂无描述' }}</p>
            </div>
            <a-divider />

            <!-- 尺码选择 -->
            <div class="size-section">
              <h3>选择尺码</h3>
              <a-radio-group
                v-model:value="selectedSize"
                button-style="solid"
                :disabled="product.stock <= 0"
              >
                <a-radio-button
                  v-for="size in product.specModelList"
                  :key="size"
                  :value="size"
                >
                  {{ size }}
                </a-radio-button>
              </a-radio-group>
              <div v-if="!selectedSize && !loading" class="size-tip">
                <a-alert message="请选择尺码" type="warning" show-icon />
              </div>
            </div>

            <!-- 数量选择 -->
            <div class="quantity-section">
              <h3>数量</h3>
              <a-input-number
                v-model:value="quantity"
                :min="1"
                :max="Math.min(10, product.stock)"
                :disabled="!selectedSize || product.stock <= 0"
              />
              <span class="quantity-tip">限购10件</span>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
              <a-button
                type="primary"
                size="large"
                :disabled="!selectedSize || product.stock <= 0"
                @click="handleAddToCart"
                :loading="addingToCart"
              >
                <template #icon>
                  <shopping-cart-outlined />
                </template>
                加入购物车
              </a-button>

              <a-button
                size="large"
                :disabled="!selectedSize || product.stock <= 0"
              >
                <template #icon>
                  <heart-outlined />
                </template>
                收藏
              </a-button>
            </div>
          </div>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useCartStore } from '../stores/cart'
import { useUserStore } from '../stores/user'
import { message } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  ShoppingCartOutlined,
  HeartOutlined
} from '@ant-design/icons-vue'
import { getProductDetail } from '../api/product'
import { handleCartAuthError } from '../utils/auth'

const route = useRoute()
const router = useRouter()
const cartStore = useCartStore()
const userStore = useUserStore()

// 设置router实例到cart store，用于认证错误处理
cartStore.setRouter(router)

// 商品状态
const product = ref(null)
const loading = ref(true)
const selectedSize = ref('')
const quantity = ref(1)
const addingToCart = ref(false)

// 图片预览状态
const previewVisible = ref(false)
const previewCurrent = ref(0)
const previewImages = ref([])

// 加载商品详情
const loadProduct = async () => {
  loading.value = true
  try {
    const response = await getProductDetail(route.params.id)
    product.value = response.data

    // 初始化数量为1
    quantity.value = 1

    // 如果库存为0，禁用选择
    if (product.value.stock <= 0) {
      message.warning('该商品已售罄')
    }
  } catch (error) {
    console.error('加载商品详情失败:', error)
    message.error('加载商品详情失败')
  } finally {
    loading.value = false
  }
}

// 添加到购物车
const handleAddToCart = async () => {
  if (!selectedSize.value) {
    message.warning('请选择尺码')
    return
  }

  if (product.value.stock < quantity.value) {
    message.warning('库存不足')
    return
  }

  try {
    addingToCart.value = true

    await cartStore.addToCart(
      product.value,
      quantity.value,
      selectedSize.value
    )

    message.success('已添加到购物车')
  } catch (error) {
    console.error('添加到购物车失败:', error)

    // 使用统一的认证错误处理
    try {
      await handleCartAuthError(error, router, '添加到购物车')
      // 如果是认证错误，已经处理了重定向，直接返回
      return
    } catch (authError) {
      // 如果不是认证错误，显示通用错误消息
      message.error('添加到购物车失败')
    }
  } finally {
    addingToCart.value = false
  }
}

onMounted(() => {
  loadProduct()
})
</script>

<style scoped>
.product-detail {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 24px;
}

.detail-container {
  max-width: 1200px;
  margin: 0 auto;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 0;
}

.header-actions {
  display: flex;
  gap: 16px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.88);
}

.product-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
  margin-top: 24px;
}

.product-gallery {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.carousel-item {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  overflow: hidden;
}

.carousel-item img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.product-info {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.product-name {
  margin: 0 0 16px;
  font-size: 24px;
  line-height: 1.4;
  color: rgba(0, 0, 0, 0.88);
  font-weight: 500;
}

.price-section {
  display: flex;
  align-items: baseline;
  gap: 12px;
  margin-bottom: 16px;
}

.current-price {
  font-size: 28px;
  color: #ff4d4f;
  font-weight: 600;
}

.original-price {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.45);
  text-decoration: line-through;
}

.sales-info {
  display: flex;
  gap: 24px;
  align-items: center;
  margin-bottom: 16px;
}

.product-description {
  margin-bottom: 16px;
}

.product-description h3 {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 500;
}

.product-description p {
  margin: 0;
  color: rgba(0, 0, 0, 0.65);
  line-height: 1.6;
}

.size-section,
.quantity-section {
  margin-bottom: 24px;
}

.size-section h3,
.quantity-section h3 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 500;
}

.size-tip {
  margin-top: 8px;
}

.quantity-tip {
  margin-left: 12px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 16px;
  margin-top: 32px;
}

@media (max-width: 768px) {
  .product-content {
    grid-template-columns: 1fr;
  }

  .product-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .carousel-item {
    height: 300px;
  }
}
</style>