{"openapi": "3.1.0", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "http://localhost:8000", "description": "Generated server url"}], "tags": [{"name": "商品接口", "description": "商品功能接口"}, {"name": "用户接口", "description": "用户功能接口"}], "paths": {"/user/update": {"put": {"tags": ["用户接口"], "summary": "更新用户", "description": "根据ID更新用户信息", "operationId": "updateUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserVO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/product/update": {"put": {"tags": ["商品接口"], "summary": "更新商品", "description": "根据ID更新商品信息", "operationId": "updateProduct", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductVO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/cart/remove": {"put": {"tags": ["cart-controller"], "summary": "从购物车中移除商品", "description": "根据购物车项ID移除商品", "operationId": "removeFromCart", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/user/logout": {"post": {"tags": ["用户接口"], "summary": "用户退出", "description": "清除用户会话信息", "operationId": "logout", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/user/login": {"post": {"tags": ["用户接口"], "summary": "用户登录", "description": "根据用户名和密码验证用户并登录", "operationId": "login", "parameters": [{"name": "username", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "password", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/user/add": {"post": {"tags": ["用户接口"], "summary": "新增用户", "description": "创建新用户", "operationId": "addUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserVO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/product/add": {"post": {"tags": ["商品接口"], "summary": "新增商品", "description": "创建新商品", "operationId": "addProduct", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductVO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/cart/add": {"post": {"tags": ["cart-controller"], "summary": "添加到购物车", "description": "添加商品到购物车", "operationId": "addToCart", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CartDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/user/detail/{id}": {"get": {"tags": ["用户接口"], "summary": "获取用户信息", "description": "通过用户id获取用户信息", "operationId": "getUserInfo", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultUserVO"}}}}}}}, "/product/list": {"get": {"tags": ["商品接口"], "summary": "获取商品列表", "description": "获取所有商品信息", "operationId": "getProductList", "parameters": [{"name": "q", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultPageEntityProductVO"}}}}}}}, "/product/detail/{id}": {"get": {"tags": ["商品接口"], "summary": "获取商品详情", "description": "通过商品ID获取商品详细信息", "operationId": "getProductInfo", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultProductVO"}}}}}}}, "/cart/page": {"get": {"tags": ["cart-controller"], "summary": "分页获取当前用户的购物车列表", "description": "根据页码和每页数量分页获取购物车列表", "operationId": "getCartPageList", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultPageEntityCartVO"}}}}}}}, "/cart/list": {"get": {"tags": ["cart-controller"], "summary": "获取当前用户的购物车列表", "description": "获取当前用户的所有购物车项", "operationId": "getCartList", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListCartVO"}}}}}}}, "/user/delete/{id}": {"delete": {"tags": ["用户接口"], "summary": "删除用户", "description": "根据ID删除用户", "operationId": "deleteUser", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/product/delete/{id}": {"delete": {"tags": ["商品接口"], "summary": "删除商品", "description": "根据ID逻辑删除商品", "operationId": "deleteProduct", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}}, "components": {"schemas": {"UserVO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "title": "主键ID"}, "createdBy": {"type": "integer", "format": "int64", "title": "创建人ID"}, "createdAt": {"type": "string", "format": "date-time", "title": "创建时间"}, "updatedBy": {"type": "integer", "format": "int64", "title": "更新人ID"}, "updatedAt": {"type": "string", "format": "date-time", "title": "更新时间"}, "deleted": {"type": "integer", "format": "int32", "title": "是否删除：0-未删除，1-已删除"}, "userName": {"type": "string", "title": "用户名"}, "avatar": {"type": "string", "title": "头像URL"}, "role": {"type": "string", "title": "角色, user: 普通用户, admin: 管理员"}}, "title": "用户VO类"}, "ResultVoid": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "title": "状态码"}, "message": {"type": "string", "title": "返回信息"}, "data": {"type": "object", "title": "返回数据"}}, "title": "统一返回结果类"}, "ProductVO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "title": "主键ID"}, "createdBy": {"type": "integer", "format": "int64", "title": "创建人ID"}, "createdAt": {"type": "string", "format": "date-time", "title": "创建时间"}, "updatedBy": {"type": "integer", "format": "int64", "title": "更新人ID"}, "updatedAt": {"type": "string", "format": "date-time", "title": "更新时间"}, "deleted": {"type": "integer", "format": "int32", "title": "是否删除：0-未删除，1-已删除"}, "productName": {"type": "string", "title": "商品名称"}, "sku": {"type": "string", "title": "SKU编码"}, "specModel": {"type": "string", "title": "当前商品规格型号"}, "originalPrice": {"type": "number", "title": "原价"}, "discountPrice": {"type": "number", "title": "折扣价"}, "discount": {"type": "number", "title": "折扣(0-1之间的小数)"}, "unit": {"type": "string", "title": "单位(￥,$)"}, "description": {"type": "string", "title": "商品描述"}, "productImage": {"type": "string", "title": "商品图片URL"}, "salesVolume": {"type": "integer", "format": "int32", "title": "销量"}, "stock": {"type": "integer", "format": "int32", "title": "库存量"}, "status": {"type": "string", "title": "状态: ON_SHELF-上架中, OFF_SHELF-已下架, PRE_SALE-预售"}, "productImageList": {"type": "array", "items": {"type": "string"}, "title": "商品图片列表"}, "specModelList": {"type": "array", "items": {"type": "string"}, "title": "该商品规格列表，根据sku划分为同一组商品"}}, "title": "商品VO"}, "ResultString": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "title": "状态码"}, "message": {"type": "string", "title": "返回信息"}, "data": {"type": "string", "title": "返回数据"}}, "title": "统一返回结果类"}, "CartDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "title": "主键ID"}, "createdBy": {"type": "integer", "format": "int64", "title": "创建人ID"}, "createdAt": {"type": "string", "format": "date-time", "title": "创建时间"}, "updatedBy": {"type": "integer", "format": "int64", "title": "更新人ID"}, "updatedAt": {"type": "string", "format": "date-time", "title": "更新时间"}, "deleted": {"type": "integer", "format": "int32", "title": "是否删除：0-未删除，1-已删除"}, "userId": {"type": "integer", "format": "int64", "title": "关联的用户ID"}, "productId": {"type": "integer", "format": "int64", "title": "关联的商品ID"}, "specModel": {"type": "string", "title": "商品规格"}, "quantity": {"type": "integer", "format": "int32", "title": "商品数量"}}}, "ResultUserVO": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "title": "状态码"}, "message": {"type": "string", "title": "返回信息"}, "data": {"$ref": "#/components/schemas/UserVO", "title": "返回数据"}}, "title": "统一返回结果类"}, "PageEntityProductVO": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32", "title": "页码"}, "limit": {"type": "integer", "format": "int32", "title": "每页数量"}, "total": {"type": "integer", "format": "int64", "title": "总数量"}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/ProductVO"}, "title": "数据列表"}}, "title": "分页实体类"}, "ResultPageEntityProductVO": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "title": "状态码"}, "message": {"type": "string", "title": "返回信息"}, "data": {"$ref": "#/components/schemas/PageEntityProductVO", "title": "返回数据"}}, "title": "统一返回结果类"}, "ResultProductVO": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "title": "状态码"}, "message": {"type": "string", "title": "返回信息"}, "data": {"$ref": "#/components/schemas/ProductVO", "title": "返回数据"}}, "title": "统一返回结果类"}, "CartVO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "title": "主键ID"}, "createdBy": {"type": "integer", "format": "int64", "title": "创建人ID"}, "createdAt": {"type": "string", "format": "date-time", "title": "创建时间"}, "updatedBy": {"type": "integer", "format": "int64", "title": "更新人ID"}, "updatedAt": {"type": "string", "format": "date-time", "title": "更新时间"}, "deleted": {"type": "integer", "format": "int32", "title": "是否删除：0-未删除，1-已删除"}, "userId": {"type": "integer", "format": "int64", "title": "关联的用户ID"}, "productId": {"type": "integer", "format": "int64", "title": "关联的商品ID"}, "productName": {"type": "string", "title": "商品名称"}, "specModel": {"type": "string", "title": "规格型号"}, "price": {"type": "number", "title": "单价"}, "unit": {"type": "string", "title": "单位(￥,$)"}, "productImage": {"type": "string", "title": "商品图片URL"}, "quantity": {"type": "integer", "format": "int32", "title": "商品数量"}}, "title": "购物车VO"}, "PageEntityCartVO": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32", "title": "页码"}, "limit": {"type": "integer", "format": "int32", "title": "每页数量"}, "total": {"type": "integer", "format": "int64", "title": "总数量"}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/CartVO"}, "title": "数据列表"}}, "title": "分页实体类"}, "ResultPageEntityCartVO": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "title": "状态码"}, "message": {"type": "string", "title": "返回信息"}, "data": {"$ref": "#/components/schemas/PageEntityCartVO", "title": "返回数据"}}, "title": "统一返回结果类"}, "ResultListCartVO": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "title": "状态码"}, "message": {"type": "string", "title": "返回信息"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CartVO"}, "title": "返回数据"}}, "title": "统一返回结果类"}}}}