<template>
  <div class="product-list">
    <!-- 商品列表 -->
    <div v-if="products.length > 0" class="product-grid">
      <div v-for="product in products" :key="product.id" class="product-card" @click="goToDetail(product.id)">
        <div class="product-image">
          <img :src="product.productImage" :alt="product.productName" loading="lazy">
        </div>
        <div class="product-info">
          <h3>{{ product.productName }}</h3>
          <div class="price-info">
            <span class="discount-price">{{ product.unit }}{{ product.discountPrice }}</span>
            <span class="original-price">{{ product.unit }}{{ product.originalPrice }}</span>
          </div>
          <div class="sales-info">
            <span>销量: {{ product.salesVolume }}</span>
            <span>  {{ product.specModel}}</span>
            <span v-if="product.discount < 1" class="discount-tag">
              {{ Math.round((1 - product.discount) * 100) }}% OFF
            </span>
          </div>
        </div>
      </div>
    </div>
    <div v-if="loading" class="loading">
      <div class="loading-spinner"></div>
      <span>加载中...</span>
    </div>
    <div v-if="!loading && products.length === 0" class="empty-state">
      <div class="empty-icon">
        <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M24 4C12.95 4 4 12.95 4 24C4 35.05 12.95 44 24 44C35.05 44 44 35.05 44 24C44 12.95 35.05 4 24 4ZM24 40C15.18 40 8 32.82 8 24C8 15.18 15.18 8 24 8C32.82 8 40 15.18 40 24C40 32.82 32.82 40 24 40Z" fill="#E0E0E0"/>
          <path d="M31 14H17C15.9 14 15 14.9 15 16V32C15 33.1 15.9 34 17 34H31C32.1 34 33 33.1 33 32V16C33 14.9 32.1 14 31 14ZM31 32H17V16H31V32Z" fill="#E0E0E0"/>
          <path d="M24 20C22.9 20 22 20.9 22 22C22 23.1 22.9 24 24 24C25.1 24 26 23.1 26 22C26 20.9 25.1 20 24 20Z" fill="#E0E0E0"/>
        </svg>
      </div>
      <p class="empty-text">暂无相关商品</p>
      <p class="empty-subtext">换个关键词试试吧</p>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getProductList } from '../api/product'
import { throttle } from 'lodash-es'

const PAGE_SIZE = 20

export default {
  name: 'ProductList',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const products = ref([])
    const loading = ref(false)
    const page = ref(1)
    const hasMore = ref(true)

    console.log("加载商品列表product_list")
    // 获取商品列表
    const fetchProducts = async (isNewSearch = false) => {
      if (!isNewSearch && (!hasMore.value || loading.value)) return
      
      loading.value = true
      if (isNewSearch) {
        page.value = 1
        products.value = []
        hasMore.value = true
      }

      try {
        const res = await getProductList(page.value, PAGE_SIZE, route.query.q || '')
        if (res.code === 200) {
          if (isNewSearch) {
            products.value = res.data.records
          } else {
            products.value = [...products.value, ...res.data.records]
          }
          console.log("商品列表: "+ products.value)
          hasMore.value = products.value.length < res.data.total
          if (hasMore.value) {
            page.value++
          }
        }
      } catch (error) {
        console.error('获取商品列表失败:', error)
      } finally {
        loading.value = false
      }
    }

    // 节流处理的滚动加载
    const handleScroll = throttle(() => {
      const scrollHeight = document.documentElement.scrollHeight
      const scrollTop = document.documentElement.scrollTop
      const clientHeight = document.documentElement.clientHeight
      
      if (scrollTop + clientHeight >= scrollHeight - 100) {
        fetchProducts()
      }
    }, 500)

    // 跳转到商品详情
    const goToDetail = (id) => {
      window.open(`/product/${id}`, '_blank')
    }

    onMounted(() => {
      fetchProducts(true)
      window.addEventListener('scroll', handleScroll)
    })

    onUnmounted(() => {
      window.removeEventListener('scroll', handleScroll)
    })

    return {
      products,
      loading,
      goToDetail
    }
  }
}
</script>

<style scoped>
.product-list {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.product-card {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s;
  background: white;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.product-image {
  width: 100%;
  aspect-ratio: 1;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  padding: 12px;
}

.product-info h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.price-info {
  margin-top: 8px;
}

.discount-price {
  font-size: 18px;
  color: #ff4400;
  font-weight: bold;
  margin-right: 8px;
}

.original-price {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
}

.sales-info {
  margin-top: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.discount-tag {
  background-color: #ff4400;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #666;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  margin-bottom: 8px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #ff4400;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 16px;
}

.empty-text {
  margin: 0;
  font-size: 16px;
  color: #666;
}

.empty-subtext {
  margin: 8px 0 0;
  font-size: 14px;
  color: #999;
}
</style> 